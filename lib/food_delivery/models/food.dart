class Food {
  final String id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final String category;
  final String restaurant;
  final double rating;
  final int reviewCount;
  final List<String> ingredients;
  final int preparationTime; // in minutes
  final bool isVegetarian;
  final bool isSpicy;
  final bool isPopular;
  final bool isAvailable;

  Food({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    required this.category,
    required this.restaurant,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.ingredients = const [],
    this.preparationTime = 30,
    this.isVegetarian = false,
    this.isSpicy = false,
    this.isPopular = false,
    this.isAvailable = true,
  });

  Food copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? imageUrl,
    String? category,
    String? restaurant,
    double? rating,
    int? reviewCount,
    List<String>? ingredients,
    int? preparationTime,
    bool? isVegetarian,
    bool? isSpicy,
    bool? isPopular,
    bool? isAvailable,
  }) {
    return Food(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      restaurant: restaurant ?? this.restaurant,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      ingredients: ingredients ?? this.ingredients,
      preparationTime: preparationTime ?? this.preparationTime,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isSpicy: isSpicy ?? this.isSpicy,
      isPopular: isPopular ?? this.isPopular,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'imageUrl': imageUrl,
      'category': category,
      'restaurant': restaurant,
      'rating': rating,
      'reviewCount': reviewCount,
      'ingredients': ingredients,
      'preparationTime': preparationTime,
      'isVegetarian': isVegetarian,
      'isSpicy': isSpicy,
      'isPopular': isPopular,
      'isAvailable': isAvailable,
    };
  }

  factory Food.fromJson(Map<String, dynamic> json) {
    return Food(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      imageUrl: json['imageUrl'] ?? '',
      category: json['category'] ?? '',
      restaurant: json['restaurant'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      ingredients: List<String>.from(json['ingredients'] ?? []),
      preparationTime: json['preparationTime'] ?? 30,
      isVegetarian: json['isVegetarian'] ?? false,
      isSpicy: json['isSpicy'] ?? false,
      isPopular: json['isPopular'] ?? false,
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  @override
  String toString() {
    return 'Food(id: $id, name: $name, price: $price, restaurant: $restaurant)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Food && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}