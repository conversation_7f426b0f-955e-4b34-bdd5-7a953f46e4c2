import 'food.dart';

class CartItem {
  final String id;
  final Food food;
  final int quantity;
  final List<String> customizations;
  final String notes;

  CartItem({
    required this.id,
    required this.food,
    this.quantity = 1,
    this.customizations = const [],
    this.notes = '',
  });

  double get totalPrice => food.price * quantity;

  CartItem copyWith({
    String? id,
    Food? food,
    int? quantity,
    List<String>? customizations,
    String? notes,
  }) {
    return CartItem(
      id: id ?? this.id,
      food: food ?? this.food,
      quantity: quantity ?? this.quantity,
      customizations: customizations ?? this.customizations,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'food': food.toJson(),
      'quantity': quantity,
      'customizations': customizations,
      'notes': notes,
    };
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'] ?? '',
      food: Food.fromJson(json['food'] ?? {}),
      quantity: json['quantity'] ?? 1,
      customizations: List<String>.from(json['customizations'] ?? []),
      notes: json['notes'] ?? '',
    );
  }

  @override
  String toString() {
    return 'CartItem(id: $id, food: ${food.name}, quantity: $quantity, total: \$${totalPrice.toStringAsFixed(2)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class Cart {
  final List<CartItem> items;
  final double deliveryFee;
  final double serviceFee;
  final double tax;

  Cart({
    this.items = const [],
    this.deliveryFee = 2.99,
    this.serviceFee = 1.99,
    this.tax = 0.0,
  });

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);

  double get calculatedTax => subtotal * 0.08; // 8% tax

  double get total => subtotal + deliveryFee + serviceFee + calculatedTax;

  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  bool get isEmpty => items.isEmpty;

  bool get isNotEmpty => items.isNotEmpty;

  Cart copyWith({
    List<CartItem>? items,
    double? deliveryFee,
    double? serviceFee,
    double? tax,
  }) {
    return Cart(
      items: items ?? this.items,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      serviceFee: serviceFee ?? this.serviceFee,
      tax: tax ?? this.tax,
    );
  }

  Cart addItem(CartItem item) {
    final existingIndex = items.indexWhere((cartItem) => cartItem.food.id == item.food.id);

    if (existingIndex >= 0) {
      final updatedItems = List<CartItem>.from(items);
      updatedItems[existingIndex] = updatedItems[existingIndex].copyWith(
        quantity: updatedItems[existingIndex].quantity + item.quantity,
      );
      return copyWith(items: updatedItems);
    } else {
      return copyWith(items: [...items, item]);
    }
  }

  Cart removeItem(String itemId) {
    return copyWith(items: items.where((item) => item.id != itemId).toList());
  }

  Cart updateItemQuantity(String itemId, int quantity) {
    if (quantity <= 0) {
      return removeItem(itemId);
    }

    final updatedItems = items.map((item) {
      if (item.id == itemId) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    return copyWith(items: updatedItems);
  }

  Cart clear() {
    return copyWith(items: []);
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'deliveryFee': deliveryFee,
      'serviceFee': serviceFee,
      'tax': tax,
    };
  }

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => CartItem.fromJson(item))
          .toList() ?? [],
      deliveryFee: (json['deliveryFee'] ?? 2.99).toDouble(),
      serviceFee: (json['serviceFee'] ?? 1.99).toDouble(),
      tax: (json['tax'] ?? 0.0).toDouble(),
    );
  }
}