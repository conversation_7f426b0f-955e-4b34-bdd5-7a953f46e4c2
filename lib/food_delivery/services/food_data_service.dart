import '../models/food.dart';
import '../models/category.dart';

class FoodDataService {
  static List<Category> getCategories() {
    return [
      Category(
        id: '1',
        name: 'Pizza',
        imageUrl: 'assets/images/pizza_category.jpg',
      ),
      Category(
        id: '2',
        name: 'Burger',
        imageUrl: 'assets/images/burger_category.jpg',
      ),
      Category(
        id: '3',
        name: 'Sushi',
        imageUrl: 'assets/images/sushi_category.jpg',
      ),
      Category(
        id: '4',
        name: 'Pasta',
        imageUrl: 'assets/images/pasta_category.jpg',
      ),
      Category(
        id: '5',
        name: 'Salad',
        imageUrl: 'assets/images/salad_category.jpg',
      ),
      Category(
        id: '6',
        name: '<PERSON>ser<PERSON>',
        imageUrl: 'assets/images/dessert_category.jpg',
      ),
    ];
  }

  static List<Food> getFoods() {
    return [
      // Pizza
      Food(
        id: '1',
        name: 'Veggie tomato mix',
        description: 'Fresh vegetables with tomato sauce and mozzarella cheese',
        price: 1900.0,
        imageUrl: 'assets/images/veggie_pizza.jpg',
        category: 'Pizza',
        restaurant: 'Mario\'s Pizza',
        rating: 4.5,
        reviewCount: 120,
        ingredients: ['Tomato', 'Mozzarella', 'Bell Peppers', 'Onions', 'Mushrooms'],
        preparationTime: 25,
        isVegetarian: true,
        isPopular: true,
      ),
      Food(
        id: '2',
        name: 'Margherita Pizza',
        description: 'Classic pizza with fresh basil, mozzarella, and tomato sauce',
        price: 1600.0,
        imageUrl: 'assets/images/margherita_pizza.jpg',
        category: 'Pizza',
        restaurant: 'Mario\'s Pizza',
        rating: 4.7,
        reviewCount: 89,
        ingredients: ['Tomato Sauce', 'Mozzarella', 'Fresh Basil'],
        preparationTime: 20,
        isVegetarian: true,
      ),

      // Burgers
      Food(
        id: '3',
        name: 'Classic Beef Burger',
        description: 'Juicy beef patty with lettuce, tomato, and special sauce',
        price: 1200.0,
        imageUrl: 'assets/images/beef_burger.jpg',
        category: 'Burger',
        restaurant: 'Burger House',
        rating: 4.3,
        reviewCount: 156,
        ingredients: ['Beef Patty', 'Lettuce', 'Tomato', 'Onion', 'Special Sauce'],
        preparationTime: 15,
        isPopular: true,
      ),
      Food(
        id: '4',
        name: 'Chicken Deluxe',
        description: 'Grilled chicken breast with avocado and bacon',
        price: 1400.0,
        imageUrl: 'assets/images/chicken_burger.jpg',
        category: 'Burger',
        restaurant: 'Burger House',
        rating: 4.6,
        reviewCount: 203,
        ingredients: ['Chicken Breast', 'Avocado', 'Bacon', 'Lettuce', 'Mayo'],
        preparationTime: 18,
      ),

      // Sushi
      Food(
        id: '5',
        name: 'Salmon Roll',
        description: 'Fresh salmon with cucumber and avocado',
        price: 2200.0,
        imageUrl: 'assets/images/salmon_roll.jpg',
        category: 'Sushi',
        restaurant: 'Sushi Master',
        rating: 4.8,
        reviewCount: 78,
        ingredients: ['Salmon', 'Cucumber', 'Avocado', 'Nori', 'Sushi Rice'],
        preparationTime: 10,
      ),
      Food(
        id: '6',
        name: 'California Roll',
        description: 'Crab, avocado, and cucumber with sesame seeds',
        price: 1800.0,
        imageUrl: 'assets/images/california_roll.jpg',
        category: 'Sushi',
        restaurant: 'Sushi Master',
        rating: 4.4,
        reviewCount: 92,
        ingredients: ['Crab', 'Avocado', 'Cucumber', 'Sesame Seeds', 'Nori'],
        preparationTime: 12,
        isPopular: true,
      ),

      // Pasta
      Food(
        id: '7',
        name: 'Spaghetti Carbonara',
        description: 'Creamy pasta with bacon, eggs, and parmesan cheese',
        price: 1500.0,
        imageUrl: 'assets/images/carbonara.jpg',
        category: 'Pasta',
        restaurant: 'Italian Corner',
        rating: 4.5,
        reviewCount: 134,
        ingredients: ['Spaghetti', 'Bacon', 'Eggs', 'Parmesan', 'Black Pepper'],
        preparationTime: 20,
      ),
      Food(
        id: '8',
        name: 'Penne Arrabbiata',
        description: 'Spicy tomato sauce with garlic and red peppers',
        price: 1300.0,
        imageUrl: 'assets/images/arrabbiata.jpg',
        category: 'Pasta',
        restaurant: 'Italian Corner',
        rating: 4.2,
        reviewCount: 67,
        ingredients: ['Penne', 'Tomato Sauce', 'Garlic', 'Red Peppers', 'Basil'],
        preparationTime: 18,
        isSpicy: true,
        isVegetarian: true,
      ),

      // Salads
      Food(
        id: '9',
        name: 'Caesar Salad',
        description: 'Crisp romaine lettuce with caesar dressing and croutons',
        price: 900.0,
        imageUrl: 'assets/images/caesar_salad.jpg',
        category: 'Salad',
        restaurant: 'Green Garden',
        rating: 4.1,
        reviewCount: 45,
        ingredients: ['Romaine Lettuce', 'Caesar Dressing', 'Croutons', 'Parmesan'],
        preparationTime: 8,
        isVegetarian: true,
      ),
      Food(
        id: '10',
        name: 'Greek Salad',
        description: 'Fresh vegetables with feta cheese and olive oil',
        price: 1100.0,
        imageUrl: 'assets/images/greek_salad.jpg',
        category: 'Salad',
        restaurant: 'Green Garden',
        rating: 4.4,
        reviewCount: 73,
        ingredients: ['Tomatoes', 'Cucumber', 'Feta Cheese', 'Olives', 'Olive Oil'],
        preparationTime: 10,
        isVegetarian: true,
      ),
    ];
  }

  static List<Food> getPopularFoods() {
    return getFoods().where((food) => food.isPopular).toList();
  }

  static List<Food> getFoodsByCategory(String categoryId) {
    final categories = getCategories();
    final category = categories.firstWhere((cat) => cat.id == categoryId);
    return getFoods().where((food) => food.category == category.name).toList();
  }

  static List<Food> searchFoods(String query) {
    if (query.isEmpty) return [];

    final lowercaseQuery = query.toLowerCase();
    return getFoods().where((food) {
      return food.name.toLowerCase().contains(lowercaseQuery) ||
             food.description.toLowerCase().contains(lowercaseQuery) ||
             food.category.toLowerCase().contains(lowercaseQuery) ||
             food.restaurant.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  static Food? getFoodById(String id) {
    try {
      return getFoods().firstWhere((food) => food.id == id);
    } catch (e) {
      return null;
    }
  }
}